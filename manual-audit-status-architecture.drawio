<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="手动审核状态管理系统架构图" id="manual-audit-status-architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 外部系统层 -->
        <mxCell id="external-layer" value="外部系统" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#424242;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="50" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="dp-system" value="DP系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#424242;" vertex="1" parent="external-layer">
          <mxGeometry x="20" y="40" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="client-system" value="客户端/前端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#424242;" vertex="1" parent="external-layer">
          <mxGeometry x="160" y="40" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- 控制层 -->
        <mxCell id="controller-layer" value="控制层 (Controller Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="400" y="50" width="400" height="120" as="geometry" />
        </mxCell>
        <mxCell id="manual-audit-controller" value="ManualAuditStatusController&#xa;人工审核状态查询接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="controller-layer">
          <mxGeometry x="20" y="40" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fraud-case-controller" value="FraudCaseController&#xa;欺诈案件控制器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="controller-layer">
          <mxGeometry x="200" y="40" width="160" height="60" as="geometry" />
        </mxCell>

        <!-- AOP切面层 -->
        <mxCell id="aspect-layer" value="AOP切面层 (Aspect Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="850" y="50" width="350" height="120" as="geometry" />
        </mxCell>
        <mxCell id="audit-status-aspect" value="AuditStatusCacheAspect&#xa;审核状态缓存切面" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="aspect-layer">
          <mxGeometry x="20" y="40" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="audit-status-annotation" value="@AuditStatusCache&#xa;审核状态缓存注解" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;strokeWidth=3;" vertex="1" parent="aspect-layer">
          <mxGeometry x="180" y="40" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 服务层 -->
        <mxCell id="service-layer" value="服务层 (Service Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="220" width="750" height="150" as="geometry" />
        </mxCell>
        <mxCell id="manual-audit-service" value="ManualAuditStatusService&#xa;审核状态服务接口" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="service-layer">
          <mxGeometry x="20" y="40" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="manual-audit-service-impl" value="ManualAuditStatusServiceImpl&#xa;审核状态服务实现" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="service-layer">
          <mxGeometry x="20" y="110" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fraud-case-service" value="FraudCaseMainService&#xa;欺诈案件主服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="service-layer">
          <mxGeometry x="200" y="40" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fraud-case-service-impl" value="FraudCaseMainServiceImpl&#xa;欺诈案件主服务实现" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="service-layer">
          <mxGeometry x="200" y="110" width="160" height="60" as="geometry" />
        </mxCell>

        <!-- 客户端层 -->
        <mxCell id="client-layer" value="客户端层 (Client Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="850" y="220" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="dp-client" value="DpClient&#xa;DP系统客户端" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="client-layer">
          <mxGeometry x="20" y="40" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 缓存层 -->
        <mxCell id="cache-layer" value="缓存层 (Cache Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="350" height="150" as="geometry" />
        </mxCell>
        <mxCell id="redis-service" value="RedisService&#xa;Redis服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="cache-layer">
          <mxGeometry x="20" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="redis-cache" value="Redis缓存&#xa;manual_audit_status:userKey" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="cache-layer">
          <mxGeometry x="180" y="40" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- 数据传输对象 -->
        <mxCell id="dto-layer" value="数据传输对象 (DTO/VO)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="450" y="420" width="500" height="150" as="geometry" />
        </mxCell>
        <mxCell id="query-dto" value="ManualAuditStatusQueryDTO&#xa;查询请求DTO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="dto-layer">
          <mxGeometry x="20" y="40" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="status-vo" value="ManualAuditStatusVO&#xa;查询响应VO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="dto-layer">
          <mxGeometry x="180" y="40" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="callback-dto" value="FraudCaseCallbackDTO&#xa;回调请求DTO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="dto-layer">
          <mxGeometry x="340" y="40" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 数据库层 -->
        <mxCell id="database-layer" value="数据库层 (Database Layer)" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="620" width="400" height="120" as="geometry" />
        </mxCell>
        <mxCell id="mysql-database" value="MySQL数据库&#xa;fraud_case_main&#xa;fraud_case_personal&#xa;inquiry_record" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="database-layer">
          <mxGeometry x="130" y="30" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- 连接线 -->
        <!-- 客户端到控制器 -->
        <mxCell id="edge1" edge="1" parent="1" source="client-system" target="manual-audit-controller">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge1-label" value="查询审核状态" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge1">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- DP系统到客户端 -->
        <mxCell id="edge2" edge="1" parent="1" source="dp-system" target="dp-client">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge2-label" value="案件回调" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge2">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- 控制器到服务 -->
        <mxCell id="edge3" edge="1" parent="1" source="manual-audit-controller" target="manual-audit-service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 服务实现关系 -->
        <mxCell id="edge4" edge="1" parent="1" source="manual-audit-service" target="manual-audit-service-impl" style="dashed=1;">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge4-label" value="实现" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge4">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- 注解到切面 -->
        <mxCell id="edge5" edge="1" parent="1" source="audit-status-annotation" target="audit-status-aspect">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge5-label" value="触发" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge5">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- 切面到服务 -->
        <mxCell id="edge6" edge="1" parent="1" source="audit-status-aspect" target="manual-audit-service">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge6-label" value="调用" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge6">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- 服务到Redis -->
        <mxCell id="edge7" edge="1" parent="1" source="manual-audit-service-impl" target="redis-service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Redis服务到Redis缓存 -->
        <mxCell id="edge8" edge="1" parent="1" source="redis-service" target="redis-cache">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge8-label" value="读写" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge8">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- 服务到数据库 -->
        <mxCell id="edge9" edge="1" parent="1" source="fraud-case-service-impl" target="mysql-database">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge9-label" value="CRUD" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge9">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- 注解标记 -->
        <mxCell id="edge10" edge="1" parent="1" source="fraud-case-service-impl" target="audit-status-annotation" style="strokeColor=#ff6600;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge10-label" value="@AuditStatusCache&#xa;ENTER_AUDIT" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fillColor=#fff8e1;" vertex="1" connectable="0" parent="edge10">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge11" edge="1" parent="1" source="dp-client" target="audit-status-annotation" style="strokeColor=#ff6600;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge11-label" value="@AuditStatusCache&#xa;EXIT_AUDIT" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fillColor=#fff8e1;" vertex="1" connectable="0" parent="edge11">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <!-- 标题 -->
        <mxCell id="title" value="手动审核状态管理系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="10" width="300" height="30" as="geometry" />
        </mxCell>

        <!-- 说明文字 -->
        <mxCell id="description" value="核心特性：&#xa;1. 基于@AuditStatusCache注解的自动状态管理&#xa;2. Redis缓存实现高并发查询支持&#xa;3. AOP切面实现业务逻辑解耦&#xa;4. 支持进入/退出审核状态的自动切换" style="text;html=1;strokeColor=#666666;fillColor=#f5f5f5;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="1100" y="420" width="280" height="120" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
